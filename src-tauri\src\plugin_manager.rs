use serde::{Deserialize, Serialize};
use std::fs;
use std::env;
use tauri::State;
use crate::settings_manager::UserPreferences;

// Helper function to resolve relative paths from app directory
fn resolve_path(path: &str) -> Result<std::path::PathBuf, String> {
    if path.starts_with("./") {
        let mut current_dir = env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;

        // Traverse up until we find a directory containing "src-tauri"
        while !current_dir.join("src-tauri").exists() {
            if !current_dir.pop() {
                return Err("Could not find project root containing 'src-tauri'".to_string());
            }
        }

        let resolved = current_dir.join(&path[2..]);
        Ok(resolved)
    } else {
        Ok(std::path::PathBuf::from(path))
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PluginVersion {
    pub version: String,
    pub main_path: String,
    pub class_name: Option<String>,
    pub features: Option<Vec<String>>,
    pub author: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Plugin {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
    pub version: String,
    pub active_version_path: Option<String>,
    pub versions: Vec<PluginVersion>,
    pub enabled: bool,
    pub path: String,
    pub app_type: Option<String>,
    pub plugin_type: Option<String>,
    pub features: Option<Vec<String>>,
    pub author: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct VersionDefinition {
    pub version: String,
    pub path: String,
    pub changelog: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct PluginJson {
    pub name: String,
    pub description: Option<String>,
    pub version: String,
    #[serde(default = "default_enabled")]
    pub enabled: bool,
    pub main: Option<String>,
    pub class_name: Option<String>,
    pub plugin_type: Option<String>,
    pub app_type: Option<String>,
    pub features: Option<Vec<String>>,
    pub author: Option<String>,
    pub versions: Option<Vec<VersionDefinition>>,
}

fn default_enabled() -> bool {
    true
}

#[tauri::command]
pub async fn get_plugins(prefs: State<'_, UserPreferences>) -> Result<Vec<Plugin>, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;

    if !plugins_dir.exists() {
        return Err(format!("Plugins directory does not exist: {}", plugins_dir.display()));
    }

    let mut plugins = Vec::new();
    let mut plugin_id = 1;

    for entry in fs::read_dir(&plugins_dir).map_err(|e| format!("Failed to read plugins directory: {}", e))? {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let plugin_path = entry.path();

        if plugin_path.is_dir() {
            // Read root plugin.json for base info and version definitions
            let root_plugin_json_path = plugin_path.join("plugin.json");
            if root_plugin_json_path.exists() {
                if let Ok(contents) = fs::read_to_string(&root_plugin_json_path) {
                    if let Ok(root_plugin_data) = serde_json::from_str::<PluginJson>(&contents) {
                        let mut versions = Vec::new();

                        // Check if versions are defined in the root plugin.json
                        if let Some(version_definitions) = &root_plugin_data.versions {
                            // New versioned plugin structure (React/JSX plugins)
                            for version_def in version_definitions {
                                let version_path = plugin_path.join(&version_def.path);
                                if version_path.exists() && version_path.is_dir() {
                                    // Find the main file for this version
                                    let default_main = "main.jsx".to_string();
                                    let main_file = root_plugin_data.main.as_ref().unwrap_or(&default_main);
                                    let main_path = version_path.join(main_file);

                                    versions.push(PluginVersion {
                                        version: version_def.version.clone(),
                                        main_path: main_path.to_string_lossy().to_string(),
                                        class_name: root_plugin_data.class_name.clone(),
                                        features: root_plugin_data.features.clone(),
                                        author: root_plugin_data.author.clone(),
                                    });
                                }
                            }
                        } else {
                            // Old-style plugin structure (Python plugins, single version)
                            let default_main = "__init__.py".to_string();
                            let main_file = root_plugin_data.main.as_ref().unwrap_or(&default_main);
                            let main_path = plugin_path.join(main_file);

                            if main_path.exists() {
                                versions.push(PluginVersion {
                                    version: root_plugin_data.version.clone(),
                                    main_path: main_path.to_string_lossy().to_string(),
                                    class_name: root_plugin_data.class_name.clone(),
                                    features: root_plugin_data.features.clone(),
                                    author: root_plugin_data.author.clone(),
                                });
                            }
                        }

                        if !versions.is_empty() {
                            // Sort versions to find the latest one (simple string sort might not be robust for semver)
                            versions.sort_by(|a, b| b.version.cmp(&a.version));
                            // Determine active version
                            let data_dir = plugin_path.join("data");
                            let active_version_file = data_dir.join("active_version.json");

                            let active_version_str = if active_version_file.exists() {
                                fs::read_to_string(active_version_file).ok()
                            } else {
                                None
                            };

                            let mut active_version_data = versions.first().cloned(); // Default to latest

                            if let Some(version_str) = active_version_str {
                                // The version string from the file might have quotes, remove them
                                let trimmed_version_str = version_str.trim().trim_matches('"');
                                if let Some(pv) = versions.iter().find(|v| v.version == trimmed_version_str) {
                                    active_version_data = Some(pv.clone());
                                }
                            }
                            
                            let active_version = active_version_data.unwrap();

                            plugins.push(Plugin {
                                id: plugin_id,
                                name: root_plugin_data.name.clone(),
                                description: root_plugin_data.description.or_else(|| Some("No description".to_string())),
                                version: active_version.version.clone(),
                                active_version_path: Some(active_version.main_path.clone()),
                                versions,
                                enabled: root_plugin_data.enabled,
                                path: plugin_path.to_string_lossy().to_string(),
                                app_type: root_plugin_data.app_type.or_else(|| Some("app".to_string())),
                                plugin_type: root_plugin_data.plugin_type.or_else(|| Some("ui_component".to_string())),
                                features: active_version.features.clone(),
                                author: active_version.author.clone(),
                            });
                            plugin_id += 1;
                        }
                    }
                }
            }
        }
    }

    println!("DEBUG: Total plugins found: {}", plugins.len());
    Ok(plugins)
}

#[tauri::command]
pub async fn set_active_plugin_version(plugin_name: String, version: String, prefs: State<'_, UserPreferences>) -> Result<String, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let data_dir = plugin_dir.join("data");

    // Create data directory if it doesn't exist
    if !data_dir.exists() {
        fs::create_dir_all(&data_dir)
            .map_err(|e| format!("Failed to create data directory: {}", e))?;
    }

    let active_version_file = data_dir.join("active_version.json");
    fs::write(&active_version_file, &version)
        .map_err(|e| format!("Failed to save active version: {}", e))?;

    Ok(format!("Set active version for {} to {}", plugin_name, version))
}

#[tauri::command]
pub async fn toggle_plugin(plugin_name: String, enabled: bool, prefs: State<'_, UserPreferences>) -> Result<String, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;

    if !plugins_dir.exists() {
        return Err(format!("Plugins directory does not exist: {}", plugins_dir.display()));
    }

    // Find the plugin directory
    for entry in fs::read_dir(&plugins_dir).map_err(|e| format!("Failed to read plugins directory: {}", e))? {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let plugin_path = entry.path();

        if plugin_path.is_dir() {
            let plugin_json_path = plugin_path.join("plugin.json");
            if plugin_json_path.exists() {
                let contents = fs::read_to_string(&plugin_json_path)
                    .map_err(|e| format!("Failed to read plugin.json: {}", e))?;
                let mut plugin_data: PluginJson = serde_json::from_str(&contents)
                    .map_err(|e| format!("Failed to parse plugin.json: {}", e))?;

                if plugin_data.name == plugin_name {
                    // Toggle the enabled state
                    plugin_data.enabled = enabled;

                    // Write back to file
                    let updated_contents = serde_json::to_string_pretty(&plugin_data)
                        .map_err(|e| format!("Failed to serialize plugin.json: {}", e))?;
                    fs::write(&plugin_json_path, updated_contents)
                        .map_err(|e| format!("Failed to write plugin.json: {}", e))?;

                    return Ok(format!("Plugin {} {}", plugin_name, if enabled { "enabled" } else { "disabled" }));
                }
            }
        }
    }

    Err(format!("Plugin {} not found", plugin_name))
}



#[tauri::command]
pub async fn save_plugin_state(plugin_name: String, state_data: String, prefs: State<'_, UserPreferences>) -> Result<String, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let data_dir = plugin_dir.join("data");
    
    // Create data directory if it doesn't exist
    if !data_dir.exists() {
        fs::create_dir_all(&data_dir)
            .map_err(|e| format!("Failed to create data directory: {}", e))?;
    }
    
    let state_file = data_dir.join("state.json");
    fs::write(&state_file, state_data)
        .map_err(|e| format!("Failed to save plugin state: {}", e))?;
    
    Ok("State saved successfully".to_string())
}

#[tauri::command]
pub async fn load_plugin_state(plugin_name: String, prefs: State<'_, UserPreferences>) -> Result<String, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let state_file = plugin_dir.join("data").join("state.json");
    
    if state_file.exists() {
        fs::read_to_string(&state_file)
            .map_err(|e| format!("Failed to load plugin state: {}", e))
    } else {
        Ok("{}".to_string())
    }
}

#[tauri::command]
pub async fn backup_plugin_data(plugin_name: String, prefs: State<'_, UserPreferences>) -> Result<String, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let data_dir = plugin_dir.join("data");
    let backup_dir = plugin_dir.join("backups");
    
    if !data_dir.exists() {
        return Err("No data directory found to backup".to_string());
    }
    
    // Create backup directory if it doesn't exist
    if !backup_dir.exists() {
        fs::create_dir_all(&backup_dir)
            .map_err(|e| format!("Failed to create backup directory: {}", e))?;
    }
    
    // Create timestamp for backup
    let timestamp = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    let backup_name = format!("backup_{}.tar", timestamp);
    let _backup_path = backup_dir.join(&backup_name);
    
    // Simple backup - just copy the entire data directory
    // In a real implementation, you might want to use proper archiving
    let backup_data_dir = backup_dir.join(format!("data_{}", timestamp));
    copy_dir_all(&data_dir, &backup_data_dir)
        .map_err(|e| format!("Failed to backup data: {}", e))?;
    
    Ok(format!("Backup created: {}", backup_name))
}

// Helper function to copy directories recursively
fn copy_dir_all(src: &std::path::Path, dst: &std::path::Path) -> std::io::Result<()> {
    fs::create_dir_all(dst)?;
    for entry in fs::read_dir(src)? {
        let entry = entry?;
        let ty = entry.file_type()?;
        if ty.is_dir() {
            copy_dir_all(&entry.path(), &dst.join(entry.file_name()))?;
        } else {
            fs::copy(entry.path(), dst.join(entry.file_name()))?;
        }
    }
    Ok(())
}

#[tauri::command]
pub async fn get_plugin_settings(plugin_name: String, prefs: State<'_, UserPreferences>) -> Result<String, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let settings_file = plugin_dir.join("data").join("settings.json");
    
    if settings_file.exists() {
        fs::read_to_string(&settings_file)
            .map_err(|e| format!("Failed to read plugin settings: {}", e))
    } else {
        Ok("{}".to_string())
    }
}

#[tauri::command]
pub async fn save_plugin_data(plugin_name: String, data_key: String, data_value: String, prefs: State<'_, UserPreferences>) -> Result<String, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let data_dir = plugin_dir.join("data");
    
    // Create data directory if it doesn't exist
    if !data_dir.exists() {
        fs::create_dir_all(&data_dir)
            .map_err(|e| format!("Failed to create data directory: {}", e))?;
    }
    
    let data_file = data_dir.join(format!("{}.json", data_key));
    fs::write(&data_file, data_value)
        .map_err(|e| format!("Failed to save plugin data: {}", e))?;
    
    Ok("Data saved successfully".to_string())
}

#[tauri::command]
pub async fn get_plugin_data(plugin_name: String, data_key: String, prefs: State<'_, UserPreferences>) -> Result<String, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let data_file = plugin_dir.join("data").join(format!("{}.json", data_key));
    
    if data_file.exists() {
        fs::read_to_string(&data_file)
            .map_err(|e| format!("Failed to read plugin data: {}", e))
    } else {
        Err("Data file not found".to_string())
    }
}
