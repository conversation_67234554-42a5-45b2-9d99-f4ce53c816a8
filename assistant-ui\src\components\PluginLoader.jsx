import React, { useState, useEffect, useMemo } from 'react';
import ErrorPopup from './ErrorPopup';
import ErrorIndicator from './ErrorIndicator';
import { useIsMounted } from '../hooks/useIsMounted.js';
import { invoke } from '@tauri-apps/api/core';

// Dynamic plugin component cache
const PluginComponentCache = new Map();

// Helper function to dynamically import plugin components
const loadPluginComponent = async (pluginName, pluginData) => {
  try {
    console.log(`🔄 Loading plugin component for: ${pluginName}`, pluginData);

    // Check cache first
    const cacheKey = `${pluginName}-${pluginData.version}`;
    if (PluginComponentCache.has(cacheKey)) {
      console.log(`✅ Using cached component for: ${pluginName}`);
      return PluginComponentCache.get(cacheKey);
    }

    // Build the import path based on plugin data
    let importPath;
    if (pluginData.active_version_path) {
      // Use the active version path from backend
      importPath = `../../../${pluginData.active_version_path}`;
    } else {
      // Fallback to constructing path from plugin data
      const basePath = pluginData.path.replace(/\\/g, '/');
      const relativePath = basePath.replace(/^.*Storage\//, '../../../Storage/');
      importPath = `${relativePath}/${pluginData.main || 'main.jsx'}`;
    }

    console.log(`📂 Attempting to import from: ${importPath}`);

    // Dynamic import with error handling
    const component = React.lazy(() =>
      import(/* @vite-ignore */ importPath).catch(error => {
        console.error(`❌ Failed to import plugin ${pluginName} from ${importPath}:`, error);
        // Return a fallback component
        return Promise.resolve({
          default: () => (
            <div className="flex items-center justify-center h-full p-8">
              <div className="text-center">
                <div className="text-red-500 mb-2">⚠️</div>
                <h3 className="text-lg font-semibold mb-2">Plugin Load Error</h3>
                <p className="text-sm text-muted-foreground mb-2">
                  Failed to load plugin: {pluginName}
                </p>
                <p className="text-xs text-muted-foreground">
                  Path: {importPath}
                </p>
                <p className="text-xs text-red-500 mt-2">
                  {error.message}
                </p>
              </div>
            </div>
          )
        });
      })
    );

    // Cache the component
    PluginComponentCache.set(cacheKey, component);
    console.log(`✅ Successfully loaded and cached plugin: ${pluginName}`);

    return component;
  } catch (error) {
    console.error(`❌ Error in loadPluginComponent for ${pluginName}:`, error);
    throw error;
  }
};

// Outer ErrorBoundary for handling errors in Suspense boundaries
class OuterErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Plugin Outer ErrorBoundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Render a minimal error display instead of blocking the entire UI
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md m-2">
          <div className="flex items-center">
            <ErrorIndicator error={this.state.error} />
            <div className="ml-2">
              <h3 className="text-sm font-medium text-red-800">Plugin Loading Error</h3>
              <p className="text-xs text-red-700 mt-1">
                Failed to load plugin. The rest of the application remains functional.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Inner ErrorBoundary for handling errors in the loaded component
class InnerErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Plugin Inner ErrorBoundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Render a minimal error display instead of blocking the entire UI
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md m-2">
          <div className="flex items-center">
            <ErrorIndicator error={this.state.error} />
            <div className="ml-2">
              <h3 className="text-sm font-medium text-red-800">Plugin Error</h3>
              <p className="text-xs text-red-700 mt-1">
                An error occurred in this plugin. Other parts of the application remain functional.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Simple ErrorBoundary component (kept for backward compatibility)
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Plugin Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Render a minimal error display instead of blocking the entire UI
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md m-2">
          <div className="flex items-center">
            <ErrorIndicator error={this.state.error} />
            <div className="ml-2">
              <h3 className="text-sm font-medium text-red-800">Plugin Error</h3>
              <p className="text-xs text-red-700 mt-1">
                An error occurred in this plugin. Other parts of the application remain functional.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

const PluginLoader = ({ pluginName, version = 'v1.0.0', ...props }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pluginData, setPluginData] = useState(null);
  const [PluginComponent, setPluginComponent] = useState(null);
  const isMounted = useIsMounted();

  useEffect(() => {
    let cancelled = false;

    // Set a timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      if (!cancelled && isMounted()) {
        console.warn(`⚠️ PluginLoader: Loading timeout for ${pluginName}`);
        setLoading(false);
        setError(`Plugin ${pluginName} failed to load (timeout)`);
      }
    }, 15000); // 15 second timeout

    const loadPlugin = async () => {
      try {
        // Check if component is still mounted
        if (cancelled || !isMounted()) return;

        console.log(`🔄 PluginLoader: Loading plugin ${pluginName}...`);
        setLoading(true);
        setError(null);

        // Step 1: Get plugin data from backend
        console.log(`📡 Fetching plugin data from backend...`);
        const plugins = await invoke('get_plugins');
        console.log(`📦 Available plugins:`, plugins);

        // Step 2: Find the requested plugin
        const plugin = plugins.find(p => p.name === pluginName);
        if (!plugin) {
          console.warn(`❌ Plugin ${pluginName} not found in backend registry`);
          if (!cancelled && isMounted()) {
            clearTimeout(loadingTimeout);
            setError(`Plugin ${pluginName} not found in registry`);
            setLoading(false);
          }
          return;
        }

        if (!plugin.enabled) {
          console.warn(`❌ Plugin ${pluginName} is disabled`);
          if (!cancelled && isMounted()) {
            clearTimeout(loadingTimeout);
            setError(`Plugin ${pluginName} is disabled`);
            setLoading(false);
          }
          return;
        }

        console.log(`✅ Found plugin data:`, plugin);
        setPluginData(plugin);

        // Step 3: Dynamically load the plugin component
        console.log(`🔄 Loading plugin component...`);
        const component = await loadPluginComponent(pluginName, plugin);

        if (!cancelled && isMounted()) {
          setPluginComponent(component);
          clearTimeout(loadingTimeout);
          setLoading(false);
          console.log(`✅ Plugin ${pluginName} loaded successfully`);
        }

      } catch (err) {
        console.error(`❌ PluginLoader: Error loading plugin ${pluginName}:`, err);
        if (!cancelled && isMounted()) {
          clearTimeout(loadingTimeout);
          setError(`Failed to load plugin: ${err.message}`);
          setLoading(false);
        }
      }
    };

    loadPlugin();

    // Cleanup function to prevent state updates on unmounted components
    return () => {
      cancelled = true;
      clearTimeout(loadingTimeout);
    };
  }, [pluginName, version, isMounted]);

  // Handle loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-400">Loading {pluginName}...</p>
        </div>
      </div>
    );
  }

  // Handle error state with a non-blocking display
  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-md m-2">
        <div className="flex items-center">
          <ErrorIndicator error={new Error(error)} />
          <div className="ml-2">
            <h3 className="text-sm font-medium text-red-800">Plugin Error</h3>
            <p className="text-xs text-red-700 mt-1">
              Failed to load the {pluginName} plugin. Other parts of the application remain functional.
            </p>
            <button 
              onClick={() => window.location.reload()}
              className="mt-2 px-3 py-1 bg-red-100 text-red-800 text-xs rounded hover:bg-red-200 transition-colors"
            >
              Reload
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Handle missing component
  if (!PluginComponent) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-md m-2">
        <div className="flex items-center">
          <ErrorIndicator />
          <div className="ml-2">
            <h3 className="text-sm font-medium text-red-800">Plugin Not Found</h3>
            <p className="text-xs text-red-700 mt-1">
              The {pluginName} plugin is not available. Other parts of the application remain functional.
            </p>
            <button 
              onClick={() => window.location.reload()}
              className="mt-2 px-3 py-1 bg-red-100 text-red-800 text-xs rounded hover:bg-red-200 transition-colors"
            >
              Reload
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Render the component with proper error boundaries and suspense
  // Using a more robust approach to handle the Suspense boundary
  return (
    <OuterErrorBoundary>
      <React.Suspense 
        fallback={
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-slate-600 dark:text-slate-400">Starting {pluginName}...</p>
            </div>
          </div>
        }
      >
        <InnerErrorBoundary>
          <PluginComponent {...props} />
        </InnerErrorBoundary>
      </React.Suspense>
    </OuterErrorBoundary>
  );
};

export default PluginLoader;