{"name": "--ci", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "concurrently \"npm run frontend:dev\" \"npm run tauri:dev\"", "frontend:dev": "cd assistant-ui && npm run dev", "backend:dev": "cd src-tauri && cargo run", "tauri:dev": "tauri dev", "build": "npm run frontend:build && npm run backend:build", "frontend:build": "cd assistant-ui && npm run build", "backend:build": "cd src-tauri && cargo build --release", "install:all": "npm run frontend:install && npm run backend:install", "frontend:install": "cd assistant-ui && npm install", "backend:install": "cd src-tauri && cargo fetch", "preview": "vite preview", "tauri": "tauri", "wait-and-test": "powershell -NoProfile -Command \"if (Test-Path 'Storage/System/logs/backend_ready.txt') { Remove-Item -Force 'Storage/System/logs/backend_ready.txt' }\" && wait-on http-get://localhost:3000 file:Storage/System/logs/backend_ready.txt && powershell -Command \"Start-Sleep -Seconds 5\" && cd assistant-ui && npx playwright test", "test": "concurrently -k -s first -n TEST,FRONT,TAURI -c \"bgGreen.bold,bgBlue.bold,bgMagenta.bold\" \"npm:wait-and-test\" \"npm:frontend:dev\" \"npm:backend:dev\""}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "concurrently": "^9.2.0", "typescript": "~5.6.2", "vite": "^6.0.3", "wait-on": "^8.0.5"}}